<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.urban.dal.mysql.task.TaskMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <!-- 根据社区ID和分割数量，对社区进行基于房屋聚类的分割 -->
    <select id="selectCommunitySubregionsBySplit" resultType="cn.iocoder.yudao.module.urban.dal.dataobject.task.CommunitySubregionDO">
        WITH
            -- 2. 提取该区域内的所有房屋中心点
            houses_in_region AS (
                SELECT h.id,
                       h.tenant_id,
                       ST_PointOnSurface(h.geom) AS house_point
                FROM   uc_basic_house h
                WHERE  ST_Within(h.geom, ST_GeomFromText(#{geom}, 4326))
            ),

            -- 其他CTE保持不变...
            clustered_houses AS (
                SELECT id,
                       tenant_id,
                       house_point,
                       ST_ClusterKMeans(house_point, #{splitCount}) OVER () AS cluster_id
                FROM   houses_in_region
            ),

            cluster_centers AS (
                SELECT cluster_id,
                       tenant_id,
                       ST_Centroid(ST_Collect(house_point)) AS center_point
                FROM   clustered_houses
                GROUP  BY cluster_id, tenant_id
            ),

            voronoi_cells AS (
                SELECT tenant_id,
                       (ST_Dump(
                               ST_VoronoiPolygons(ST_Collect(center_point))
                        )).geom AS voronoi_geom
        FROM   cluster_centers
        GROUP  BY tenant_id
            ),

            -- 6. 用传入的几何区域裁剪 Voronoi 多边形
            final_subregions AS (
        SELECT row_number() OVER () AS subregion_id,
            v.tenant_id,
            ST_Intersection(v.voronoi_geom, ST_GeomFromText(#{geom}, 4326)) AS geom
        FROM   voronoi_cells v
        WHERE  ST_Intersects(v.voronoi_geom, ST_GeomFromText(#{geom}, 4326))
            )

        SELECT
            f.subregion_id,
            ST_AsText(f.geom) AS geom,
            ST_Area(f.geom) AS area,
            COUNT(h.id) AS house_count,
            STRING_AGG(h.id::text, ',' ORDER BY h.id) AS house_ids
        FROM final_subregions f
                 LEFT JOIN uc_basic_house h ON ST_Within(h.geom, f.geom)
            AND h.tenant_id = f.tenant_id
        GROUP BY f.subregion_id, f.tenant_id, f.geom
        ORDER BY f.subregion_id
    </select>

    <!-- 分页查询任务，同时关联查询执行人信息 -->
    <select id="selectPageWithExecutors" resultType="cn.iocoder.yudao.module.urban.dal.dataobject.task.TaskDO">
        SELECT
            t.id,
            t.task_id,
            t.citystandard_id,
            t.task_type,
            t.task_name,
            t.province,
            t.city,
            t.xzqdm,
            t.town,
            t.village,
            t.community,
            t.leader_id,
            t.leader_name,
            t.status,
            t.planned_time,
            t.actual_date,
            t.geom,
            t.creator,
            t.create_time,
            t.updater,
            t.update_time,
            t.tenant_id,
            STRING_AGG(te.user_id::text, ',' ORDER BY te.user_id) as surveyor_ids_str
        FROM uc_task t
        LEFT JOIN uc_task_executor te ON t.id = te.task_id
        <where>
            <if test="taskId != null and taskId != ''">
                AND t.task_id = #{taskId}
            </if>
            <if test="taskType != null and taskType != ''">
                AND t.task_type = #{taskType}
            </if>
            <if test="taskName != null and taskName != ''">
                AND t.task_name LIKE CONCAT('%', #{taskName}, '%')
            </if>
            <if test="province != null and province != ''">
                AND t.province = #{province}
            </if>
            <if test="city != null and city != ''">
                AND t.city = #{city}
            </if>
            <if test="xzqdm != null and xzqdm != ''">
                AND t.xzqdm = #{xzqdm}
            </if>
            <if test="town != null and town != ''">
                AND t.town = #{town}
            </if>
            <if test="village != null and village != ''">
                AND t.village = #{village}
            </if>
            <if test="community != null and community != ''">
                AND t.community = #{community}
            </if>
            <if test="leaderName != null and leaderName != ''">
                AND t.leader_name LIKE CONCAT('%', #{leaderName}, '%')
            </if>
            <if test="status != null">
                AND t.status = #{status}
            </if>
            <if test="plannedTime != null and plannedTime.length == 2">
                AND t.planned_time BETWEEN #{plannedTime[0]} AND #{plannedTime[1]}
            </if>
            <if test="actualDate != null and actualDate.length == 2">
                AND t.actual_date BETWEEN #{actualDate[0]} AND #{actualDate[1]}
            </if>
            <if test="createTime != null and createTime.length == 2">
                AND t.create_time BETWEEN #{createTime[0]} AND #{createTime[1]}
            </if>
            <if test="geom != null and geom != ''">
                AND t.geom = #{geom}
            </if>
        </where>
        GROUP BY t.id, t.task_id, t.citystandard_id, t.task_type, t.task_name, t.province, t.city, t.xzqdm,
                 t.town, t.village, t.community, t.leader_id, t.leader_name, t.status, t.planned_time,
                 t.actual_date, t.geom, t.creator, t.create_time, t.updater, t.update_time, t.tenant_id
        ORDER BY t.id DESC
    </select>

</mapper>